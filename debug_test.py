#!/usr/bin/env python3

import tempfile
from pathlib import Path
from unittest.mock import MagicMock, patch

def test_file_discovery():
    temp_dir = Path(tempfile.mkdtemp())
    print(f'Temp dir: {temp_dir}')

    test_files = {
        'main.py': 'def main():\n    print("Hello World")',
        'utils.py': 'def helper():\n    return True',
        'README.md': '# Project\n\nThis is a test project.',
        'config.json': '{"setting": "value"}',
    }

    for filename, content in test_files.items():
        file_path = temp_dir / filename
        file_path.write_text(content)
        print(f'Created: {file_path} (exists: {file_path.exists()}, size: {file_path.stat().st_size})')

    # Test file discovery
    include_patterns = [
        "*.py", "*.js", "*.ts", "*.jsx", "*.tsx", "*.java", "*.cpp", "*.c", "*.h", "*.hpp",
        "*.cs", "*.go", "*.rs", "*.rb", "*.php", "*.swift", "*.kt", "*.scala", "*.clj",
        "*.hs", "*.ml", "*.r", "*.sql", "*.md", "*.rst", "*.txt", "*.yaml", "*.yml",
        "*.json", "*.toml", "*.cfg", "*.ini", "*.conf",
    ]

    total_found = 0
    for pattern in include_patterns:
        files = list(temp_dir.rglob(pattern))
        if files:
            print(f'Pattern {pattern}: {files}')
            total_found += len(files)

    print(f'Total files found: {total_found}')

    # Test the actual vector store logic
    print("\n--- Testing Vector Store Logic ---")
    from banana_forge.vector_store import CodeVectorStore

    with patch.object(CodeVectorStore, "_setup_client"):
        vector_store = CodeVectorStore()
        vector_store.collection = MagicMock()

        # Mock collection.get to return empty results (documents don't exist)
        vector_store.collection.get.return_value = {"ids": []}
        # Mock collection.count to return a reasonable number
        vector_store.collection.count.return_value = 4
        # Mock collection.add to track calls
        vector_store.collection.add = MagicMock()

        # Index the files - pass the temp_dir explicitly
        stats = vector_store.index_project_files(temp_dir)

        print(f"Stats: {stats}")
        print(f"Collection.add call count: {vector_store.collection.add.call_count}")
        print(f"Collection.add calls: {vector_store.collection.add.call_args_list}")

    # Clean up
    import shutil
    shutil.rmtree(temp_dir, ignore_errors=True)

if __name__ == "__main__":
    test_file_discovery()
