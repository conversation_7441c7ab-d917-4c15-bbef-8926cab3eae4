"""
Tests for the LLM client module.
"""

from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import httpx
import pytest

from banana_forge.llm import LLMClient


class TestLLMClient:
    """Test cases for LLMClient."""

    def test_llm_client_initialization(self):
        """Test LLM client initialization."""
        with patch.object(LLMClient, "_setup_clients"):
            client = LLMClient()
            assert client is not None

    def test_is_local_model(self):
        """Test local model detection."""
        with patch.object(LLMClient, "_setup_clients"):
            client = LLMClient()

            # Test local models
            assert client.is_local_model("qwen2.5:8b") is True
            assert client.is_local_model("llama2") is True
            assert client.is_local_model("mistral") is True

            # Test remote models
            assert client.is_local_model("openai/gpt-4") is False
            assert client.is_local_model("anthropic/claude-3") is False
            assert client.is_local_model("moonshot/kimi-k2") is False

    def test_setup_clients(self):
        """Test client setup."""
        with patch("banana_forge.llm.httpx.Client") as mock_httpx:
            with patch("banana_forge.llm.settings") as mock_settings:
                mock_client = MagicMock()
                mock_httpx.return_value = mock_client

                # Mock settings to provide API key
                mock_settings.openrouter_api_key = "test-api-key"
                mock_settings.openrouter_base_url = "https://openrouter.ai/api/v1"
                mock_settings.ollama_base_url = "http://localhost:11434"
                mock_settings.verbose = False

                client = LLMClient()

                # Should have created clients
                assert client.is_openrouter_available() is True
                assert client.is_ollama_available() is True

    def test_generate_completion_openrouter(self):
        """Test completion generation with OpenRouter."""
        with patch.object(LLMClient, "_setup_clients"):
            with patch.object(LLMClient, "_generate_openrouter_completion") as mock_gen:
                mock_gen.return_value = "Test response"

                client = LLMClient()

                result = client.generate_completion(
                    prompt="Test prompt", model="openai/gpt-4", max_tokens=100
                )

                assert result == "Test response"
                mock_gen.assert_called_once()

    def test_generate_completion_ollama(self):
        """Test completion generation with Ollama."""
        with patch.object(LLMClient, "_setup_clients"):
            with patch.object(LLMClient, "_generate_ollama_completion") as mock_gen:
                mock_gen.return_value = "Test response from Ollama"

                client = LLMClient()

                result = client.generate_completion(
                    prompt="Test prompt", model="qwen2.5:8b", max_tokens=100
                )

                assert result == "Test response from Ollama"
                mock_gen.assert_called_once()

    @patch("banana_forge.llm.httpx.Client")
    def test_health_check(self, mock_httpx: Mock):
        """Test health check functionality."""
        # Mock successful responses
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None

        mock_client = Mock()
        mock_client.get.return_value = mock_response
        mock_httpx.return_value = mock_client

        client = LLMClient()
        health = client.health_check()

        assert "openrouter" in health
        assert "ollama" in health

    @patch("banana_forge.llm.httpx.Client")
    def test_health_check_with_failures(self, mock_httpx: Mock):
        """Test health check with service failures."""
        # Mock failed responses
        mock_client = Mock()
        mock_client.get.side_effect = httpx.RequestError("Connection failed")
        mock_httpx.return_value = mock_client

        client = LLMClient()
        health = client.health_check()

        assert "openrouter" in health
        assert "ollama" in health
        assert health["openrouter"] is False
        assert health["ollama"] is False

    @patch("banana_forge.llm.httpx.Client")
    def test_list_available_models(self, mock_httpx: Mock):
        """Test listing available models."""
        mock_client = Mock()
        mock_httpx.return_value = mock_client

        client = LLMClient()
        models = client.list_available_models()

        assert "openrouter" in models
        assert "ollama" in models
        assert isinstance(models["openrouter"], list)
        assert isinstance(models["ollama"], list)

    def test_generate_completion_error_handling(self):
        """Test error handling in completion generation."""
        with patch.object(LLMClient, "_setup_clients"):
            with patch.object(LLMClient, "_generate_openrouter_completion") as mock_gen:
                mock_gen.side_effect = Exception("Network error")

                client = LLMClient()

                with pytest.raises(Exception):
                    client.generate_completion(
                        prompt="Test prompt", model="openai/gpt-4", max_tokens=100
                    )

    def test_model_routing(self):
        """Test that models are routed to correct providers."""
        with patch.object(LLMClient, "_setup_clients"):
            with patch.object(LLMClient, "_generate_openrouter_completion") as mock_or:
                with patch.object(LLMClient, "_generate_ollama_completion") as mock_ol:
                    client = LLMClient()

                    # Test OpenRouter routing
                    mock_or.return_value = "OpenRouter response"
                    result = client.generate_completion("test", "openai/gpt-4")
                    assert result == "OpenRouter response"
                    mock_or.assert_called_once()

                    # Test Ollama routing
                    mock_ol.return_value = "Ollama response"
                    result = client.generate_completion("test", "qwen2.5:8b")
                    assert result == "Ollama response"
                    mock_ol.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__])
